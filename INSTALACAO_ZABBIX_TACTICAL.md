# Instalação Automatizada - Zabbix + Tactical RMM

Este repositório contém scripts para instalação automatizada do Zabbix Proxy, Zabbix Agent e Tactical RMM em Ubuntu Server 24.04, incluindo configuração automática de IP fixo.

## Arquivos Incluídos

- `install_zabbix_tactical_rmm.sh` - Script principal de instalação
- `check_installation.sh` - Script de verificação e troubleshooting
- `INSTALACAO_ZABBIX_TACTICAL.md` - Este arquivo de documentação

## Pré-requisitos

- Ubuntu Server 24.04 LTS (instalação limpa)
- Acesso à internet
- Usuário com privilégios sudo (não executar como root)
- Conexão de rede ativa (DHCP inicialmente)

## Funcionalidades

### Configuração Automática de Rede
- Detecta automaticamente o IP atual obtido via DHCP
- Identifica a faixa de rede (ex: 192.168.15.x)
- Configura IP fixo terminando em .222 (ex: **************)
- Preserva gateway e DNS existentes
- Aplica configuração via netplan

### Instalação do Zabbix
- **Zabbix Proxy 7.0** com SQLite3
- **Zabbix Agent** para monitoramento local
- Configuração automática para servidor `monitora.nvirtual.com.br`
- Otimizações de performance (pollers, pingers, etc.)
- Habilitação de comandos remotos

### Instalação do Tactical RMM
- Download e execução do script oficial
- Configuração interativa durante a instalação

### Configuração de Segurança
- Firewall UFW com regras básicas
- Portas liberadas: 22, 80, 443, 10050, 10051

## Como Usar

### 1. Preparação
```bash
# Fazer download dos scripts
wget https://raw.githubusercontent.com/seu-usuario/seu-repo/main/install_zabbix_tactical_rmm.sh
wget https://raw.githubusercontent.com/seu-usuario/seu-repo/main/check_installation.sh

# Dar permissão de execução
chmod +x install_zabbix_tactical_rmm.sh
chmod +x check_installation.sh
```

### 2. Execução da Instalação
```bash
# Executar o script principal
./install_zabbix_tactical_rmm.sh
```

### 3. Informações Solicitadas
Durante a execução, o script solicitará:

1. **Nome do Zabbix Proxy**: Nome único para identificar este proxy no servidor Zabbix
   - Exemplo: `cliente-zbxproxy`, `empresa-proxy01`
   
2. **Confirmação do IP fixo**: O script detectará automaticamente a rede e sugerirá um IP terminando em .222
   - Exemplo: Se o IP atual for `*************`, será sugerido `**************`

### 4. Verificação da Instalação
```bash
# Executar script de verificação
./check_installation.sh
```

## Configurações Aplicadas

### Zabbix Proxy (`/etc/zabbix/zabbix_proxy.conf`)
```ini
Server=monitora.nvirtual.com.br
Hostname=[nome-informado]
StartPollers=20
StartPingers=10
StartDiscoverers=10
StartPollersUnreachable=10
EnableRemoteCommands=1
DBName=/tmp/zabbix_proxy.db
```

### Zabbix Agent (`/etc/zabbix/zabbix_agentd.conf`)
```ini
Server=127.0.0.1,monitora.nvirtual.com.br
ServerActive=127.0.0.1,monitora.nvirtual.com.br
Hostname=[nome-informado]
EnableRemoteCommands=1
```

### Configuração de Rede (`/etc/netplan/01-netcfg.yaml`)
```yaml
network:
  version: 2
  renderer: networkd
  ethernets:
    [interface]:
      dhcp4: false
      addresses:
        - [ip-fixo]/24
      gateway4: [gateway-detectado]
      nameservers:
        addresses: [dns-detectados]
```

## Troubleshooting

### Verificar Status dos Serviços
```bash
sudo systemctl status zabbix-proxy
sudo systemctl status zabbix-agent
```

### Verificar Logs
```bash
# Logs em tempo real
sudo journalctl -u zabbix-proxy -f
sudo journalctl -u zabbix-agent -f

# Arquivos de log
sudo tail -f /var/log/zabbix/zabbix_proxy.log
sudo tail -f /var/log/zabbix/zabbix_agentd.log
```

### Testar Conectividade
```bash
# Testar conexão com servidor Zabbix
telnet monitora.nvirtual.com.br 10051

# Testar agent local
zabbix_get -s localhost -k system.hostname
```

### Reiniciar Serviços
```bash
sudo systemctl restart zabbix-proxy
sudo systemctl restart zabbix-agent
```

### Problemas Comuns

#### 1. Falha na Conectividade Após Configurar IP Fixo
```bash
# Verificar configuração
cat /etc/netplan/01-netcfg.yaml

# Reaplicar configuração
sudo netplan apply

# Restaurar backup se necessário
sudo cp /etc/netplan/01-netcfg.yaml.backup.* /etc/netplan/01-netcfg.yaml
sudo netplan apply
```

#### 2. Zabbix Proxy Não Conecta ao Servidor
```bash
# Verificar conectividade
ping monitora.nvirtual.com.br
telnet monitora.nvirtual.com.br 10051

# Verificar configuração
grep -E "^(Server|Hostname)" /etc/zabbix/zabbix_proxy.conf
```

#### 3. Problemas de Firewall
```bash
# Verificar status
sudo ufw status

# Reconfigurar se necessário
sudo ufw allow 10050/tcp
sudo ufw allow 10051/tcp
sudo ufw reload
```

## Portas Utilizadas

| Serviço | Porta | Protocolo | Descrição |
|---------|-------|-----------|-----------|
| SSH | 22 | TCP | Acesso remoto |
| HTTP | 80 | TCP | Tactical RMM |
| HTTPS | 443 | TCP | Tactical RMM |
| Zabbix Agent | 10050 | TCP | Monitoramento |
| Zabbix Proxy | 10051 | TCP | Comunicação com servidor |

## Arquivos de Backup

O script cria backups automáticos dos arquivos de configuração:
- `/etc/netplan/01-netcfg.yaml.backup.[timestamp]`
- `/etc/zabbix/zabbix_proxy.conf.backup`
- `/etc/zabbix/zabbix_agentd.conf.backup`

## Suporte

Para problemas ou dúvidas:
1. Execute o script `check_installation.sh` para diagnóstico
2. Verifique os logs dos serviços
3. Consulte a documentação oficial do Zabbix e Tactical RMM

## Changelog

### v1.0
- Instalação automatizada do Zabbix 7.0
- Configuração automática de IP fixo
- Instalação do Tactical RMM
- Scripts de verificação e troubleshooting
